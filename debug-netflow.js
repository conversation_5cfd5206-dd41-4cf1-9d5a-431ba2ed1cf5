// Debug script for Bybit Net Flow issues
// Run this in the browser console to diagnose connection problems

(function() {
    console.log('=== Bybit Net Flow Debug Tool ===');
    
    // Function to run comprehensive diagnostics
    function runDiagnostics() {
        console.log('\n--- Connection Diagnostics ---');
        
        // Check basic WebSocket manager
        const wsManager = window.bybitWsManager;
        if (!wsManager) {
            console.error('❌ No Bybit WebSocket manager found');
            return;
        }
        
        console.log('✓ Bybit WebSocket manager exists');
        console.log('Connection state:', wsManager.isConnected() ? '✓ Connected' : '❌ Disconnected');
        console.log('WebSocket readyState:', wsManager.ws?.readyState);
        console.log('Navigator online:', navigator.onLine);
        console.log('Document hidden:', document.hidden);
        
        // Check current pair and expected subscription
        const currentPair = window.currentPair || window.currentActivePair || 'BTC';
        const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;
        console.log('Current pair:', currentPair);
        console.log('Expected channel:', expectedChannel);
        
        // Check subscriptions
        console.log('\n--- Subscription Status ---');
        const subscriptions = wsManager.subscriptions ? Array.from(wsManager.subscriptions.keys()) : [];
        console.log('All subscriptions:', subscriptions);
        
        const hasExpectedSub = wsManager.subscriptions?.has(expectedChannel);
        console.log('Has expected subscription:', hasExpectedSub ? '✓ Yes' : '❌ No');
        
        if (!hasExpectedSub) {
            console.log('🔧 Missing subscription - looking for similar channels:');
            subscriptions.forEach(sub => {
                if (sub.includes('publicTrade')) {
                    console.log('  Found trade channel:', sub);
                }
            });
        }
        
        // Check handlers
        const handlers = wsManager.handlers ? Array.from(wsManager.handlers.keys()) : [];
        console.log('Handler channels:', handlers);
        
        // Check net flow specific status
        console.log('\n--- Net Flow Status ---');
        if (window.PS?.checkNetFlowStatus) {
            window.PS.checkNetFlowStatus();
        } else {
            console.warn('❌ Net flow status checker not available');
        }
        
        // Check for recent trades
        if (window.PS?.addBybitTrade) {
            console.log('✓ addBybitTrade function exists');
        } else {
            console.error('❌ addBybitTrade function missing');
        }
        
        return {
            wsManager,
            currentPair,
            expectedChannel,
            hasExpectedSub,
            subscriptions,
            handlers
        };
    }
    
    // Function to test manual trade injection
    function testTradeInjection() {
        console.log('\n--- Testing Trade Injection ---');
        
        if (!window.PS?.addBybitTrade) {
            console.error('❌ addBybitTrade function not available');
            return;
        }
        
        const testTrade = {
            side: 'Buy',
            price: 50000,
            size: 0.1
        };
        
        console.log('Injecting test trade:', testTrade);
        window.PS.addBybitTrade(testTrade);
        
        setTimeout(() => {
            const netFlowEl = document.getElementById('bybit-net-flow');
            if (netFlowEl) {
                console.log('Net flow display after test trade:', netFlowEl.textContent);
            }
        }, 100);
    }
    
    // Function to force reconnection
    function forceReconnection() {
        console.log('\n--- Forcing Reconnection ---');
        
        const wsManager = window.bybitWsManager;
        if (!wsManager) {
            console.error('❌ No WebSocket manager');
            return;
        }
        
        console.log('Forcing reconnection...');
        wsManager.reconnect(true);
        
        setTimeout(() => {
            console.log('Reconnection attempt completed');
            runDiagnostics();
        }, 3000);
    }
    
    // Function to force resubscription
    function forceResubscription() {
        console.log('\n--- Forcing Resubscription ---');

        if (window.PS?.ensureNetFlowSubscription) {
            const result = window.PS.ensureNetFlowSubscription();
            console.log('Ensure subscription result:', result);

            setTimeout(() => {
                console.log('Resubscription attempt completed');
                runDiagnostics();
            }, 2000);
        } else if (window.PS?.forceNetFlowResubscription) {
            window.PS.forceNetFlowResubscription();

            setTimeout(() => {
                console.log('Resubscription attempt completed');
                runDiagnostics();
            }, 2000);
        } else {
            console.error('❌ No resubscription functions available');
        }
    }
    
    // Make functions available globally for manual testing
    window.debugNetFlow = {
        runDiagnostics,
        testTradeInjection,
        forceReconnection,
        forceResubscription,
        
        // Quick commands
        status: runDiagnostics,
        test: testTradeInjection,
        reconnect: forceReconnection,
        resub: forceResubscription
    };
    
    console.log('\n=== Debug Functions Available ===');
    console.log('debugNetFlow.status() - Run diagnostics');
    console.log('debugNetFlow.test() - Test trade injection');
    console.log('debugNetFlow.reconnect() - Force reconnection');
    console.log('debugNetFlow.resub() - Force resubscription');
    console.log('\nOr use shortcuts:');
    console.log('debugNetFlow.runDiagnostics()');
    console.log('debugNetFlow.testTradeInjection()');
    console.log('debugNetFlow.forceReconnection()');
    console.log('debugNetFlow.forceResubscription()');
    
    // Run initial diagnostics
    console.log('\n=== Initial Diagnostics ===');
    runDiagnostics();
    
})();
