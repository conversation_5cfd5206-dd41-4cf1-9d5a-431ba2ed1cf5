// Manual test to trigger indicator updates and verify they're working
// Run this in browser console to force updates and test the system

(function() {
  'use strict';
  
  console.log('🧪 MANUAL INDICATOR UPDATE TEST');
  console.log('===============================');
  
  // Function to create test data
  function createTestBar(time) {
    return {
      time: time,
      open: 50000 + Math.random() * 1000,
      high: 50500 + Math.random() * 1000,
      low: 49500 + Math.random() * 1000,
      close: 50000 + Math.random() * 1000,
      volume: 100 + Math.random() * 50
    };
  }
  
  // Get current 5-minute bar time
  const now = Math.floor(Date.now() / 1000);
  const currentBarTime = Math.floor(now / 300) * 300;
  
  console.log('Current 5-min bar time:', new Date(currentBarTime * 1000).toISOString());
  
  // Test 1: Check if data stores exist and can be called
  console.log('\n📊 TEST 1: Data Store Availability');
  
  if (window.PS) {
    console.log('✅ window.PS exists');
    
    if (window.PS.setPerpCVDPriceData) {
      console.log('✅ setPerpCVDPriceData available');
      
      // Create test data for PERP CVD
      const testBars = [];
      for (let i = 0; i < 10; i++) {
        testBars.push(createTestBar(currentBarTime - (9 - i) * 300));
      }
      
      try {
        console.log('🔄 Triggering setPerpCVDPriceData with', testBars.length, 'bars');
        window.PS.setPerpCVDPriceData(testBars);
        console.log('✅ setPerpCVDPriceData call successful');
      } catch (e) {
        console.log('❌ setPerpCVDPriceData failed:', e);
      }
    } else {
      console.log('❌ setPerpCVDPriceData not available');
    }
    
    if (window.PS.setPerpImbalanceSourceData) {
      console.log('✅ setPerpImbalanceSourceData available');
      
      // Create test data for PERP Imbalance
      const spotBars = [];
      const futuresBars = [];
      for (let i = 0; i < 10; i++) {
        spotBars.push(createTestBar(currentBarTime - (9 - i) * 300));
        futuresBars.push(createTestBar(currentBarTime - (9 - i) * 300));
      }
      
      try {
        console.log('🔄 Triggering setPerpImbalanceSourceData');
        window.PS.setPerpImbalanceSourceData({
          spot: spotBars,
          futures: futuresBars,
          oi: []
        });
        console.log('✅ setPerpImbalanceSourceData call successful');
      } catch (e) {
        console.log('❌ setPerpImbalanceSourceData failed:', e);
      }
    } else {
      console.log('❌ setPerpImbalanceSourceData not available');
    }
  } else {
    console.log('❌ window.PS not available');
  }
  
  // Test 2: Check subscription system
  console.log('\n📡 TEST 2: Subscription System');
  
  let subscriptionTests = {
    cvd: { attempted: false, received: false, unsubscribe: null },
    perpCvd: { attempted: false, received: false, unsubscribe: null },
    perpImbalance: { attempted: false, received: false, unsubscribe: null }
  };
  
  // Test CVD subscription
  if (window.PS && window.PS.subscribeCVD) {
    subscriptionTests.cvd.attempted = true;
    subscriptionTests.cvd.unsubscribe = window.PS.subscribeCVD((data) => {
      console.log('📨 CVD data received:', data);
      subscriptionTests.cvd.received = true;
    });
    console.log('✅ CVD subscription attempted');
  }
  
  // Test PERP CVD subscription
  if (window.PS && window.PS.subscribePerpCVD) {
    subscriptionTests.perpCvd.attempted = true;
    subscriptionTests.perpCvd.unsubscribe = window.PS.subscribePerpCVD((data) => {
      console.log('📨 PERP CVD data received:', data);
      subscriptionTests.perpCvd.received = true;
    });
    console.log('✅ PERP CVD subscription attempted');
  }
  
  // Test PERP Imbalance subscription
  if (window.PS && window.PS.subscribePerpImbalance) {
    subscriptionTests.perpImbalance.attempted = true;
    subscriptionTests.perpImbalance.unsubscribe = window.PS.subscribePerpImbalance((data) => {
      console.log('📨 PERP Imbalance data received:', data);
      subscriptionTests.perpImbalance.received = true;
    });
    console.log('✅ PERP Imbalance subscription attempted');
  }
  
  // Test 3: Force data updates
  console.log('\n🔄 TEST 3: Force Data Updates');
  
  setTimeout(() => {
    // Try to force updates by calling setPerpCVD and setPerpImbalance directly
    if (window.PS && window.PS.setPerpCVD) {
      console.log('🔄 Forcing PERP CVD update');
      window.PS.setPerpCVD({
        time: currentBarTime,
        value: Math.random() * 2 - 1 // Random normalized value between -1 and 1
      });
    }
    
    if (window.PS && window.PS.setPerpImbalance) {
      console.log('🔄 Forcing PERP Imbalance update');
      window.PS.setPerpImbalance({
        time: currentBarTime,
        value: Math.random() * 2 - 1 // Random normalized value between -1 and 1
      });
    }
  }, 2000);
  
  // Test 4: Check results after 5 seconds
  setTimeout(() => {
    console.log('\n📋 TEST RESULTS SUMMARY:');
    console.log('========================');
    
    Object.entries(subscriptionTests).forEach(([key, test]) => {
      const name = key.toUpperCase().replace('PERP', 'PERP ');
      if (test.attempted) {
        const status = test.received ? '✅ WORKING' : '⚠️  NO DATA RECEIVED';
        console.log(`${name}: ${status}`);
      } else {
        console.log(`${name}: ❌ NOT AVAILABLE`);
      }
    });
    
    // Clean up subscriptions
    Object.values(subscriptionTests).forEach(test => {
      if (test.unsubscribe) {
        try {
          test.unsubscribe();
        } catch (e) {
          console.log('Warning: Error unsubscribing:', e);
        }
      }
    });
    
    console.log('\n💡 If indicators show "NO DATA RECEIVED", they may need more time or the data stores may not be properly initialized.');
    console.log('💡 Check the main console for any error messages or loading issues.');
    
  }, 5000);
  
  console.log('\n⏳ Running tests... Results will appear in 5 seconds.');
  
})();
