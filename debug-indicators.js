// Debug script to verify perpcvd and perpimbalance indicators are updating every 5 minutes
// Run this in browser console to monitor indicator updates

(function() {
  'use strict';
  
  console.log('🔍 Starting Indicator Update Verification...');
  
  // Track update times for each indicator
  const updateTracker = {
    cvd: { lastUpdate: 0, updateCount: 0, name: '<PERSON><PERSON> (Reference)' },
    perpCvd: { lastUpdate: 0, updateCount: 0, name: 'PERP CVD' },
    perpImbalance: { lastUpdate: 0, updateCount: 0, name: 'PERP Imbalance' }
  };
  
  // Function to log update with timestamp
  function logUpdate(indicator, data) {
    const now = Date.now();
    const tracker = updateTracker[indicator];
    tracker.lastUpdate = now;
    tracker.updateCount++;
    
    const timeStr = new Date(now).toISOString().substr(11, 8);
    console.log(`📊 ${tracker.name} Update #${tracker.updateCount} at ${timeStr}:`, data);
  }
  
  // Monitor CVD updates (reference)
  if (window.PS && window.PS.subscribeCVD) {
    window.PS.subscribeCVD((data) => {
      logUpdate('cvd', data);
    });
    console.log('✅ Monitoring CVD updates (reference indicator)');
  } else {
    console.warn('❌ CVD subscription not available');
  }
  
  // Monitor PERP CVD updates
  if (window.PS && window.PS.subscribePerpCVD) {
    window.PS.subscribePerpCVD((data) => {
      logUpdate('perpCvd', data);
    });
    console.log('✅ Monitoring PERP CVD updates');
  } else {
    console.warn('❌ PERP CVD subscription not available');
  }
  
  // Monitor PERP Imbalance updates
  if (window.PS && window.PS.subscribePerpImbalance) {
    window.PS.subscribePerpImbalance((data) => {
      logUpdate('perpImbalance', data);
    });
    console.log('✅ Monitoring PERP Imbalance updates');
  } else {
    console.warn('❌ PERP Imbalance subscription not available');
  }
  
  // Check current 5-minute bar timing
  function getCurrentBarInfo() {
    const now = Math.floor(Date.now() / 1000);
    const currentBarTime = Math.floor(now / 300) * 300;
    const secondsIntoBar = now % 300;
    const nextBarIn = 300 - secondsIntoBar;
    
    return {
      currentBarTime,
      secondsIntoBar,
      nextBarIn,
      currentBarTimeStr: new Date(currentBarTime * 1000).toISOString(),
      nextBarTimeStr: new Date((currentBarTime + 300) * 1000).toISOString()
    };
  }
  
  // Display current bar timing info
  function showBarTiming() {
    const barInfo = getCurrentBarInfo();
    console.log(`⏰ Current 5-min bar: ${barInfo.currentBarTimeStr}`);
    console.log(`⏰ Seconds into bar: ${barInfo.secondsIntoBar}/300`);
    console.log(`⏰ Next bar in: ${barInfo.nextBarIn} seconds (${barInfo.nextBarTimeStr})`);
  }
  
  // Show initial timing
  showBarTiming();
  
  // Monitor for bar close events
  let lastBarTime = getCurrentBarInfo().currentBarTime;
  const barCloseMonitor = setInterval(() => {
    const barInfo = getCurrentBarInfo();
    
    // Check if we've moved to a new 5-minute bar
    if (barInfo.currentBarTime > lastBarTime) {
      console.log('🔔 NEW 5-MINUTE BAR DETECTED!');
      console.log(`   Previous bar: ${new Date(lastBarTime * 1000).toISOString()}`);
      console.log(`   Current bar:  ${barInfo.currentBarTimeStr}`);
      
      // Check which indicators have updated recently (within last 30 seconds)
      const recentThreshold = 30000; // 30 seconds
      const now = Date.now();
      
      Object.entries(updateTracker).forEach(([key, tracker]) => {
        const timeSinceUpdate = now - tracker.lastUpdate;
        const isRecent = timeSinceUpdate < recentThreshold;
        const status = isRecent ? '✅ UPDATED' : '❌ NO RECENT UPDATE';
        console.log(`   ${tracker.name}: ${status} (${Math.round(timeSinceUpdate/1000)}s ago)`);
      });
      
      lastBarTime = barInfo.currentBarTime;
    }
    
    // Show countdown to next bar close every 30 seconds
    if (barInfo.secondsIntoBar % 30 === 0) {
      console.log(`⏱️  Next bar close in ${barInfo.nextBarIn} seconds`);
    }
  }, 5000); // Check every 5 seconds
  
  // Summary report every 2 minutes
  const summaryInterval = setInterval(() => {
    console.log('\n📈 INDICATOR UPDATE SUMMARY:');
    Object.entries(updateTracker).forEach(([key, tracker]) => {
      const timeSinceUpdate = Date.now() - tracker.lastUpdate;
      const minutesAgo = Math.round(timeSinceUpdate / 60000);
      console.log(`   ${tracker.name}: ${tracker.updateCount} updates, last ${minutesAgo}m ago`);
    });
    showBarTiming();
    console.log('');
  }, 120000); // Every 2 minutes
  
  // Cleanup function
  window.stopIndicatorVerification = () => {
    clearInterval(barCloseMonitor);
    clearInterval(summaryInterval);
    console.log('🛑 Stopped indicator verification monitoring');
  };
  
  console.log('🚀 Indicator verification started! Watch for updates every 5 minutes.');
  console.log('💡 Run stopIndicatorVerification() to stop monitoring');
  
})();
