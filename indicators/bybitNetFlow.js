// Bybit Net Flow (5m) Floating Indicator for Charts
// Author: Expert Engineer

(function () {
    // --- CONFIG ---
    const defaultConfig = {
        windows: [
            { label: '1m', ms: 1 * 60 * 1000 },
            { label: '5m', ms: 5 * 60 * 1000 },
            { label: '15m', ms: 15 * 60 * 1000 }
        ],
        defaultWindowIndex: 1,
        colors: {
            positive: "rgba(0, 255, 255, 0.75)",
            negative: "rgba(255, 85, 85, 0.75)",
            neutral: "#BBBBBB"
        }
    };
    const BYBIT_NET_FLOW_CONFIG = window.CONFIG?.bybitNetFlow || defaultConfig;

    const NET_FLOW_WINDOWS = BYBIT_NET_FLOW_CONFIG.windows;
    let netFlowWindowIdx = BYBIT_NET_FLOW_CONFIG.defaultWindowIndex;
    let NET_FLOW_WINDOW_MS = NET_FLOW_WINDOWS[netFlowWindowIdx].ms;

    // --- STATE ---
    let netFlowTrades = []; // { time, side, price, size, dollarValue }

    // --- UI CREATION ---
    function createNetFlowWindow() {
        if (document.getElementById('bybit-net-flow-window')) {
            return;
        }
        window.PS = window.PS || {};
        window.PS.createNetFlowWindow = createNetFlowWindow;
        const div = document.createElement('div');
        div.id = 'bybit-net-flow-window';
        div.className = 'net-flow-btn';
        div.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 32px;
            min-width: 90px;
            padding: 0 12px;
            font-size: 15px;
            font-family: inherit;
            /* background removed */
            color: #fff;
            border: none;
            border-radius: 4px;
            margin-left: 12px;
            margin-right: 0;
            box-shadow: none;
            position: absolute;
            right: 0;
            top: 0;
            pointer-events: auto;
            cursor: default;
            vertical-align: middle;
            z-index: 10;
            background: none;
        `;
        div.innerHTML = `
            <span style="font-size:13px;opacity:0.7;margin-right:6px;">NetFlow</span>
            <span id="bybit-net-flow" style="font-size:12px;font-family:'Arial',sans-serif;color:#BBBBBB;line-height:1.2;">$0</span>
            <button id="bybit-net-flow-window-btn" class="net-flow-window-btn" style="margin-left:8px;">1m</button>
        `;
        // Wait for pair selector if not present yet
        function tryAppend() {
            const pairSelector = document.querySelector('.pair-selector');
            if (pairSelector) {
                // Ensure parent is positioned relative for absolute positioning
                if (getComputedStyle(pairSelector).position === 'static') {
                    pairSelector.style.position = 'relative';
                }
                pairSelector.appendChild(div);
                // Add event listener for window button
                const windowBtn = document.getElementById('bybit-net-flow-window-btn');
                if (windowBtn) {
                    windowBtn.addEventListener('click', function() {
                        netFlowWindowIdx = (netFlowWindowIdx + 1) % NET_FLOW_WINDOWS.length;
                        NET_FLOW_WINDOW_MS = NET_FLOW_WINDOWS[netFlowWindowIdx].ms;
                        windowBtn.textContent = NET_FLOW_WINDOWS[netFlowWindowIdx].label;
                        // Remove trades outside the new window, aligned to global time boundary
                        const now = Date.now();
                        const boundary = now - (now % NET_FLOW_WINDOW_MS);
                        while (netFlowTrades.length && netFlowTrades[0].time < boundary - NET_FLOW_WINDOW_MS) {
                            netFlowTrades.shift();
                        }
                        updateNetFlowDisplay();
                    });
                }
            } else {
                setTimeout(tryAppend, 300);
            }
        }
        tryAppend();
    }

    // --- LOGIC ---
    function addBybitTrade(trade) {
        if (!trade || !trade.side || !trade.price || !trade.size) return;
        const now = Date.now();
        const dollarValue = trade.price * trade.size;
        netFlowTrades.push({
            time: now,
            side: trade.side,
            price: trade.price,
            size: trade.size,
            dollarValue
        });
        // Remove trades outside the window
        while (netFlowTrades.length && netFlowTrades[0].time < now - NET_FLOW_WINDOW_MS) {
            netFlowTrades.shift();
        }
        updateNetFlowDisplay();
    }

    function updateNetFlowDisplay() {
        let buy = 0, sell = 0;
        for (const t of netFlowTrades) {
            if (t.side === 'Buy') buy += t.dollarValue;
            else if (t.side === 'Sell') sell += t.dollarValue;
        }
        const net = buy - sell;
        const el = document.getElementById('bybit-net-flow');
        if (el) {
            el.textContent = net.toLocaleString(undefined, { style: 'currency', currency: 'USD', maximumFractionDigits: 0 });
            const colors = BYBIT_NET_FLOW_CONFIG.colors;
            el.style.color = net > 0 ? colors.positive : net < 0 ? colors.negative : colors.neutral;
            el.style.fontSize = "12px";
            el.style.fontFamily = "'Arial',sans-serif";
            el.style.lineHeight = "1.2";
        }
    }

    // --- EXPORT/HANDLER HOOK ---
    // Attach to window for integration with Bybit trade stream
    window.PS = window.PS || {};
    window.PS.addBybitTrade = addBybitTrade;



    // Debug function to check WebSocket connection status
    window.PS.checkNetFlowStatus = function() {
        const currentPair = window.currentPair || window.currentActivePair || 'BTC';
        const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;
        const wsManager = window.bybitWsManager;

        console.log('[NetFlow] Status check:', {
            currentPair: currentPair,
            expectedChannel: expectedChannel,
            bybitWsConnected: wsManager?.isConnected?.() || false,
            bybitWsState: wsManager?.ws?.readyState,
            bybitSubscriptions: wsManager?.subscriptions ? Array.from(wsManager.subscriptions.keys()) : [],
            bybitHandlers: wsManager?.handlers ? Array.from(wsManager.handlers.keys()) : [],
            addBybitTradeExists: typeof window.PS.addBybitTrade === 'function',
            netFlowElement: !!document.getElementById('bybit-net-flow'),
            netFlowWindow: !!document.getElementById('bybit-net-flow-window'),
            pairSelector: !!document.querySelector('.pair-selector'),
            currentTradesCount: netFlowTrades.length,
            currentWindowMs: NET_FLOW_WINDOW_MS,
            lastTradeTime: lastTradeTime,
            timeSinceLastTrade: lastTradeTime ? Date.now() - lastTradeTime : 'never',
            documentHidden: document.hidden,
            navigatorOnline: navigator.onLine
        });

        // Check if we're subscribed to the right channel
        if (wsManager?.subscriptions?.has(expectedChannel)) {
            console.log('[NetFlow] ✓ Subscribed to expected channel:', expectedChannel);
        } else {
            console.warn('[NetFlow] ✗ NOT subscribed to expected channel:', expectedChannel);
            console.log('[NetFlow] Available subscriptions:', wsManager?.subscriptions ? Array.from(wsManager.subscriptions.keys()) : 'none');
        }
    };

    // Reset function to clear state and UI when chart switches
    window.PS = window.PS || {};
    window.PS.resetNetFlow = function() {
        // Fully reset state and UI for netflow on chart switch
        netFlowTrades = [];
        netFlowWindowIdx = 1; // Reset to 5m
        NET_FLOW_WINDOW_MS = NET_FLOW_WINDOWS[netFlowWindowIdx].ms;
        // Reset UI to default
        const el = document.getElementById('bybit-net-flow');
        if (el) {
            el.textContent = "$0";
            el.style.color = "#BBBBBB";
        }
        const windowBtn = document.getElementById('bybit-net-flow-window-btn');
        if (windowBtn) {
            windowBtn.textContent = NET_FLOW_WINDOWS[netFlowWindowIdx].label;
        }
    };

    // Independent subscription management for net flow
    let netFlowSubscriptionHandler = null;

    function ensureNetFlowSubscription() {
        const currentPair = window.currentPair || window.currentActivePair || 'BTC';
        const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;
        const wsManager = window.bybitWsManager;

        if (!wsManager || !wsManager.isConnected()) {
            console.warn('[NetFlow] WebSocket not available for subscription');
            return false;
        }

        // Create our own handler if it doesn't exist
        if (!netFlowSubscriptionHandler) {
            netFlowSubscriptionHandler = (message) => {
                if (message.topic?.startsWith("publicTrade.") && message.data) {
                    const trades = Array.isArray(message.data) ? message.data : [message.data];
                    trades.forEach((trade) => {
                        if (trade && trade.S && trade.p && trade.v) {
                            window.PS?.addBybitTrade?.({
                                side: trade.S,
                                price: parseFloat(trade.p),
                                size: parseFloat(trade.v),
                            });
                        }
                    });
                }
            };
        }

        // Check if we already have a subscription
        if (wsManager.subscriptions?.has(expectedChannel)) {
            console.log('[NetFlow] Subscription already exists for:', expectedChannel);
            return true;
        }

        // Subscribe to the channel
        console.log('[NetFlow] Creating subscription for:', expectedChannel);
        wsManager.subscribe(expectedChannel, netFlowSubscriptionHandler);
        return true;
    }

    // Force resubscription function for debugging
    window.PS.forceNetFlowResubscription = function() {
        const currentPair = window.currentPair || window.currentActivePair || 'BTC';
        const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;
        const wsManager = window.bybitWsManager;

        if (!wsManager) {
            console.error('[NetFlow] No Bybit WebSocket manager found');
            return;
        }

        console.log('[NetFlow] Forcing resubscription to:', expectedChannel);

        // First unsubscribe if already subscribed
        if (wsManager.subscriptions?.has(expectedChannel)) {
            console.log('[NetFlow] Unsubscribing from existing channel');
            wsManager.unsubscribe(expectedChannel);
        }

        // Wait a moment then resubscribe
        setTimeout(() => {
            ensureNetFlowSubscription();
        }, 500);
    };

    // Ensure subscription on initialization
    window.PS.ensureNetFlowSubscription = ensureNetFlowSubscription;

    // Cleanup function for proper resource management
    window.PS.cleanupNetFlow = function() {
        if (connectionCheckInterval) {
            clearInterval(connectionCheckInterval);
            connectionCheckInterval = null;
        }
        if (visibilityReconnectTimeout) {
            clearTimeout(visibilityReconnectTimeout);
            visibilityReconnectTimeout = null;
        }
        document.removeEventListener('visibilitychange', handleVisibilityChange);
    };

    // Connection health monitoring
    let lastTradeTime = 0;
    let connectionCheckInterval = null;
    let visibilityReconnectTimeout = null;
    let wasHidden = false;

    function startConnectionMonitoring() {
        if (connectionCheckInterval) return;

        connectionCheckInterval = setInterval(() => {
            const now = Date.now();
            const timeSinceLastTrade = now - lastTradeTime;
            const currentPair = window.currentPair || window.currentActivePair || 'BTC';
            const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;

            // Check subscription health
            if (window.bybitWsManager?.isConnected()) {
                if (!window.bybitWsManager.subscriptions?.has(expectedChannel)) {
                    console.warn('[NetFlow] Missing subscription detected during health check:', expectedChannel);
                    ensureNetFlowSubscription();
                }
            }

            // If no trades received for 30 seconds, show connection warning
            if (timeSinceLastTrade > 30000 && lastTradeTime > 0) {
                const el = document.getElementById('bybit-net-flow');
                if (el && !el.textContent.includes('...')) {
                    el.style.opacity = '0.5';
                    el.title = 'Connection may be unstable - no recent trades';
                }
            } else {
                const el = document.getElementById('bybit-net-flow');
                if (el) {
                    el.style.opacity = '1';
                    el.title = '';
                }
            }
        }, 5000);
    }

    // Enhanced visibility change handler for better reconnection
    function handleVisibilityChange() {
        if (document.hidden) {
            wasHidden = true;
            // Clear any pending reconnection attempts
            if (visibilityReconnectTimeout) {
                clearTimeout(visibilityReconnectTimeout);
                visibilityReconnectTimeout = null;
            }
        } else if (wasHidden) {
            // Tab became visible after being hidden
            wasHidden = false;

            // Force reconnection after a short delay to ensure WebSocket is ready
            visibilityReconnectTimeout = setTimeout(() => {
                if (window.bybitWsManager) {
                    console.log('[NetFlow] Tab visible - checking Bybit connection');

                    const currentPair = window.currentPair || window.currentActivePair || 'BTC';
                    const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;

                    // Force reconnection if not connected
                    if (!window.bybitWsManager.isConnected()) {
                        console.log('[NetFlow] Forcing Bybit reconnection after tab visibility change');
                        window.bybitWsManager.reconnect(true);

                        // Wait for reconnection then check subscription
                        setTimeout(() => {
                            if (!window.bybitWsManager.subscriptions?.has(expectedChannel)) {
                                console.log('[NetFlow] Missing subscription after reconnect, ensuring subscription');
                                ensureNetFlowSubscription();
                            }
                        }, 2000);
                    } else {
                        // Even if connected, verify subscription and send a ping
                        if (!window.bybitWsManager.subscriptions?.has(expectedChannel)) {
                            console.log('[NetFlow] Connected but missing subscription, ensuring subscription');
                            ensureNetFlowSubscription();
                        } else {
                            try {
                                window.bybitWsManager._sendPing();
                            } catch (e) {
                                console.warn('[NetFlow] Ping failed, forcing reconnection:', e);
                                window.bybitWsManager.reconnect(true);
                            }
                        }
                    }
                }
                visibilityReconnectTimeout = null;
            }, 1000);
        }
    }

    // Update lastTradeTime when trades are received
    const originalAddBybitTrade = addBybitTrade;
    addBybitTrade = function(trade) {
        lastTradeTime = Date.now();
        return originalAddBybitTrade(trade);
    };

    // Listen for WebSocket reconnection events
    window.addEventListener('websocket-connected-bybit', function() {
        // Reset connection status when Bybit WebSocket reconnects
        lastTradeTime = Date.now();
        const el = document.getElementById('bybit-net-flow');
        if (el) {
            el.style.opacity = '1';
            el.title = '';
        }
        console.log('[NetFlow] Bybit WebSocket reconnected');

        // Ensure our subscription is restored after reconnection
        setTimeout(() => {
            const currentPair = window.currentPair || window.currentActivePair || 'BTC';
            const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;

            if (!window.bybitWsManager?.subscriptions?.has(expectedChannel)) {
                console.log('[NetFlow] Subscription missing after reconnection, ensuring subscription');
                ensureNetFlowSubscription();
            } else {
                console.log('[NetFlow] Subscription verified after reconnection');
            }
        }, 1000);
    });

    // Listen for visibility restoration events (new event from improved WebSocket manager)
    window.addEventListener('websocket-visibility-restored-bybit', function(event) {
        console.log('[NetFlow] Bybit WebSocket visibility restored after', event.detail.hiddenDuration, 'ms');

        // Force subscription verification after visibility restoration
        setTimeout(() => {
            const currentPair = window.currentPair || window.currentActivePair || 'BTC';
            const expectedChannel = `publicTrade.${currentPair.toUpperCase()}USDT`;

            if (!window.bybitWsManager?.subscriptions?.has(expectedChannel)) {
                console.log('[NetFlow] Subscription missing after visibility restoration, ensuring subscription');
                ensureNetFlowSubscription();
            } else {
                console.log('[NetFlow] Subscription verified after visibility restoration');
                // Even if subscription exists, test it by checking for recent data
                const timeSinceLastTrade = Date.now() - lastTradeTime;
                if (timeSinceLastTrade > 60000) { // No trades for 1 minute
                    console.log('[NetFlow] No recent trades despite subscription, forcing resubscription');
                    window.PS.forceNetFlowResubscription();
                }
            }
        }, 2000);
    });

    // Add visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // --- INIT ---
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            createNetFlowWindow();
            startConnectionMonitoring();

            // Ensure subscription after a short delay to allow WebSocket to be ready
            setTimeout(() => {
                if (window.bybitWsManager?.isConnected()) {
                    ensureNetFlowSubscription();
                }
            }, 2000);
        });
    } else {
        createNetFlowWindow();
        startConnectionMonitoring();

        // Ensure subscription after a short delay to allow WebSocket to be ready
        setTimeout(() => {
            if (window.bybitWsManager?.isConnected()) {
                ensureNetFlowSubscription();
            }
        }, 2000);
    }
})();
