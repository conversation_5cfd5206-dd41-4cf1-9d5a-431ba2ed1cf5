// Comprehensive debug script for Bybit WebSocket visibility issues
// Run this in the browser console to test the fixes

(function() {
    console.log('=== Bybit Visibility Fix Debug Tool ===');
    
    // Track visibility changes
    let visibilityLog = [];
    let originalVisibilityHandler = null;
    
    function logVisibilityChange(state, timestamp = Date.now()) {
        const entry = {
            state: state,
            timestamp: timestamp,
            time: new Date(timestamp).toISOString()
        };
        visibilityLog.push(entry);
        console.log(`[Visibility] ${state} at ${entry.time}`);
        
        // Keep only last 20 entries
        if (visibilityLog.length > 20) {
            visibilityLog = visibilityLog.slice(-20);
        }
    }
    
    // Monitor visibility changes
    function startVisibilityMonitoring() {
        document.addEventListener('visibilitychange', () => {
            logVisibilityChange(document.hidden ? 'HIDDEN' : 'VISIBLE');
        });
        
        // Monitor WebSocket events
        window.addEventListener('websocket-connected-bybit', (event) => {
            console.log('[WS Event] Bybit WebSocket connected:', event.detail);
        });
        
        window.addEventListener('websocket-visibility-restored-bybit', (event) => {
            console.log('[WS Event] Bybit visibility restored:', event.detail);
        });
    }
    
    // Test WebSocket manager state
    function checkWebSocketState() {
        const wsManager = window.bybitWsManager;
        if (!wsManager) {
            console.error('❌ No Bybit WebSocket manager found');
            return null;
        }
        
        const state = {
            connected: wsManager.isConnected(),
            wsReadyState: wsManager.ws?.readyState,
            subscriptions: wsManager.subscriptions ? Array.from(wsManager.subscriptions.keys()) : [],
            lastHiddenTime: wsManager.lastHiddenTime,
            lastPongTime: wsManager.lastPongTime,
            reconnectAttempts: wsManager.reconnectAttempts,
            networkStatus: wsManager.networkStatus,
            pingTimer: !!wsManager.pingTimer
        };
        
        console.log('[WS State]', state);
        return state;
    }
    
    // Test all indicators
    function checkIndicatorStates() {
        console.log('\n--- Indicator States ---');
        
        // Net Flow
        if (window.PS?.checkNetFlowStatus) {
            console.log('[Net Flow] Status:');
            window.PS.checkNetFlowStatus();
        } else {
            console.warn('[Net Flow] Status checker not available');
        }
        
        // Perp CVD
        if (window.PS?.pendingPerpCVDUpdates) {
            const cvdState = window.PS.pendingPerpCVDUpdates;
            console.log('[Perp CVD] State:', {
                hasUpdate: cvdState.hasUpdate,
                lastSuccessTime: cvdState.lastSuccessTime,
                connectionErrors: cvdState.connectionErrors,
                timeSinceLastSuccess: Date.now() - (cvdState.lastSuccessTime || 0)
            });
        } else {
            console.warn('[Perp CVD] State not available');
        }
        
        // Perp Imbalance
        if (window.PS?.pendingPerpImbalanceUpdates) {
            const imbalanceState = window.PS.pendingPerpImbalanceUpdates;
            console.log('[Perp Imbalance] State:', {
                hasUpdate: imbalanceState.hasUpdate,
                lastSuccessTime: imbalanceState.lastSuccessTime,
                connectionErrors: imbalanceState.connectionErrors,
                timeSinceLastSuccess: Date.now() - (imbalanceState.lastSuccessTime || 0)
            });
        } else {
            console.warn('[Perp Imbalance] State not available');
        }
    }
    
    // Simulate visibility change for testing
    function simulateVisibilityChange(hidden = true) {
        console.log(`\n--- Simulating Visibility Change: ${hidden ? 'HIDDEN' : 'VISIBLE'} ---`);
        
        // Manually trigger the WebSocket manager's visibility handler
        if (window.bybitWsManager?._handleVisibilityChange) {
            // Temporarily change document.visibilityState
            const originalState = document.visibilityState;
            Object.defineProperty(document, 'visibilityState', {
                value: hidden ? 'hidden' : 'visible',
                configurable: true
            });
            
            window.bybitWsManager._handleVisibilityChange();
            
            // Restore original state
            Object.defineProperty(document, 'visibilityState', {
                value: originalState,
                configurable: true
            });
            
            logVisibilityChange(hidden ? 'SIMULATED_HIDDEN' : 'SIMULATED_VISIBLE');
        } else {
            console.error('❌ WebSocket visibility handler not available');
        }
    }
    
    // Force all indicators to resubscribe
    function forceAllResubscriptions() {
        console.log('\n--- Forcing All Resubscriptions ---');
        
        // Net Flow
        if (window.PS?.ensureNetFlowSubscription) {
            console.log('[Net Flow] Ensuring subscription...');
            window.PS.ensureNetFlowSubscription();
        }
        
        // Force WebSocket manager resubscription
        if (window.bybitWsManager?.resubscribeAll) {
            console.log('[WebSocket] Forcing resubscribe all...');
            window.bybitWsManager.resubscribeAll();
        }
        
        setTimeout(() => {
            console.log('Resubscription attempts completed');
            checkIndicatorStates();
        }, 3000);
    }
    
    // Run comprehensive diagnostics
    function runFullDiagnostics() {
        console.log('\n=== Full Diagnostics ===');
        console.log('Current time:', new Date().toISOString());
        console.log('Document hidden:', document.hidden);
        console.log('Navigator online:', navigator.onLine);
        
        checkWebSocketState();
        checkIndicatorStates();
        
        console.log('\nVisibility log (last 10):');
        visibilityLog.slice(-10).forEach(entry => {
            console.log(`  ${entry.time}: ${entry.state}`);
        });
    }
    
    // Make functions available globally
    window.debugVisibilityFix = {
        checkWebSocketState,
        checkIndicatorStates,
        simulateVisibilityChange,
        forceAllResubscriptions,
        runFullDiagnostics,
        getVisibilityLog: () => visibilityLog,
        
        // Shortcuts
        check: runFullDiagnostics,
        resub: forceAllResubscriptions,
        simHidden: () => simulateVisibilityChange(true),
        simVisible: () => simulateVisibilityChange(false)
    };
    
    // Start monitoring
    startVisibilityMonitoring();
    
    console.log('\n=== Debug Functions Available ===');
    console.log('debugVisibilityFix.check() - Run full diagnostics');
    console.log('debugVisibilityFix.resub() - Force all resubscriptions');
    console.log('debugVisibilityFix.simHidden() - Simulate tab hidden');
    console.log('debugVisibilityFix.simVisible() - Simulate tab visible');
    console.log('debugVisibilityFix.getVisibilityLog() - Get visibility change log');
    
    // Run initial diagnostics
    console.log('\n=== Initial Diagnostics ===');
    runFullDiagnostics();
    
})();
