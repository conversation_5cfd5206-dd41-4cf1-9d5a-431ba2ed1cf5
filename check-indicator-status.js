// Quick diagnostic script to check current indicator status
// Run this in browser console to see current state

(function() {
  'use strict';
  
  console.log('🔍 INDICATOR STATUS DIAGNOSTIC');
  console.log('================================');
  
  // Check if main modules are loaded
  console.log('\n📦 MODULE AVAILABILITY:');
  console.log('window.PS:', !!window.PS);
  console.log('window.cvdModule:', !!window.cvdModule);
  console.log('window.perpCvdModule:', !!window.perpCvdModule);
  console.log('window.perpImbalance:', !!window.perpImbalance);
  
  // Check data store functions
  if (window.PS) {
    console.log('\n🗄️  DATA STORE FUNCTIONS:');
    console.log('setCVDPriceData:', typeof window.PS.setCVDPriceData);
    console.log('setPerpCVDPriceData:', typeof window.PS.setPerpCVDPriceData);
    console.log('setPerpImbalanceSourceData:', typeof window.PS.setPerpImbalanceSourceData);
    console.log('subscribeCVD:', typeof window.PS.subscribeCVD);
    console.log('subscribePerpCVD:', typeof window.PS.subscribePerpCVD);
    console.log('subscribePerpImbalance:', typeof window.PS.subscribePerpImbalance);
  }
  
  // Check current data
  if (window.PS) {
    console.log('\n📊 CURRENT DATA:');
    
    if (window.PS.getCurrentCVD) {
      const cvdData = window.PS.getCurrentCVD();
      console.log('CVD Data:', cvdData);
    }
    
    if (window.PS.getCurrentPerpCVD) {
      const perpCvdData = window.PS.getCurrentPerpCVD();
      console.log('PERP CVD Data:', perpCvdData);
    }
    
    if (window.PS.getCurrentPerpImbalance) {
      const perpImbalanceData = window.PS.getCurrentPerpImbalance();
      console.log('PERP Imbalance Data:', perpImbalanceData);
    }
  }
  
  // Check pending updates state
  if (window.PS) {
    console.log('\n⏳ PENDING UPDATES STATE:');
    
    if (window.PS.pendingCVDUpdates) {
      console.log('CVD Pending:', {
        lastBarTime: window.PS.pendingCVDUpdates.lastBarTime,
        hasUpdate: window.PS.pendingCVDUpdates.hasUpdate,
        pendingValue: window.PS.pendingCVDUpdates.pendingValue
      });
    }
    
    if (window.PS.pendingPerpCVDUpdates) {
      console.log('PERP CVD Pending:', {
        lastBarTime: window.PS.pendingPerpCVDUpdates.lastBarTime,
        hasUpdate: window.PS.pendingPerpCVDUpdates.hasUpdate,
        pendingValue: window.PS.pendingPerpCVDUpdates.pendingValue
      });
    }
    
    if (window.PS.pendingPerpImbalanceUpdates) {
      console.log('PERP Imbalance Pending:', {
        lastBarTime: window.PS.pendingPerpImbalanceUpdates.lastBarTime,
        hasUpdate: window.PS.pendingPerpImbalanceUpdates.hasUpdate,
        pendingValue: window.PS.pendingPerpImbalanceUpdates.pendingValue
      });
    }
  }
  
  // Check chart components
  console.log('\n📈 CHART COMPONENTS:');
  if (window.priceChart) {
    console.log('Price Chart:', !!window.priceChart);
    
    // Try to get series info
    try {
      const panes = window.priceChart.panes();
      console.log('Chart Panes:', panes ? panes.length : 'N/A');
    } catch (e) {
      console.log('Chart Panes: Error accessing');
    }
  }
  
  // Check websocket connections
  console.log('\n🌐 WEBSOCKET STATUS:');
  if (window.bitstampWsManager) {
    console.log('Bitstamp WS:', {
      connected: window.bitstampWsManager.connected,
      connecting: window.bitstampWsManager.connecting
    });
  }
  
  if (window.bybitWsManager) {
    console.log('Bybit WS:', {
      connected: window.bybitWsManager.connected,
      connecting: window.bybitWsManager.connecting
    });
  }
  
  // Current time info
  const now = Math.floor(Date.now() / 1000);
  const currentBarTime = Math.floor(now / 300) * 300;
  const secondsIntoBar = now % 300;
  
  console.log('\n⏰ CURRENT TIME INFO:');
  console.log('Current Time:', new Date().toISOString());
  console.log('Current 5-min Bar:', new Date(currentBarTime * 1000).toISOString());
  console.log('Seconds into Bar:', secondsIntoBar, '/ 300');
  console.log('Next Bar in:', 300 - secondsIntoBar, 'seconds');
  
  // Test subscription functions
  console.log('\n🧪 TESTING SUBSCRIPTIONS:');
  
  let testResults = {
    cvd: false,
    perpCvd: false,
    perpImbalance: false
  };
  
  // Test CVD subscription
  if (window.PS && window.PS.subscribeCVD) {
    try {
      const unsubscribe = window.PS.subscribeCVD((data) => {
        console.log('✅ CVD subscription test successful:', data);
        testResults.cvd = true;
        unsubscribe();
      });
      setTimeout(() => {
        if (!testResults.cvd) {
          console.log('⚠️  CVD subscription test: no immediate data');
          unsubscribe();
        }
      }, 1000);
    } catch (e) {
      console.log('❌ CVD subscription test failed:', e);
    }
  }
  
  // Test PERP CVD subscription
  if (window.PS && window.PS.subscribePerpCVD) {
    try {
      const unsubscribe = window.PS.subscribePerpCVD((data) => {
        console.log('✅ PERP CVD subscription test successful:', data);
        testResults.perpCvd = true;
        unsubscribe();
      });
      setTimeout(() => {
        if (!testResults.perpCvd) {
          console.log('⚠️  PERP CVD subscription test: no immediate data');
          unsubscribe();
        }
      }, 1000);
    } catch (e) {
      console.log('❌ PERP CVD subscription test failed:', e);
    }
  }
  
  // Test PERP Imbalance subscription
  if (window.PS && window.PS.subscribePerpImbalance) {
    try {
      const unsubscribe = window.PS.subscribePerpImbalance((data) => {
        console.log('✅ PERP Imbalance subscription test successful:', data);
        testResults.perpImbalance = true;
        unsubscribe();
      });
      setTimeout(() => {
        if (!testResults.perpImbalance) {
          console.log('⚠️  PERP Imbalance subscription test: no immediate data');
          unsubscribe();
        }
      }, 1000);
    } catch (e) {
      console.log('❌ PERP Imbalance subscription test failed:', e);
    }
  }
  
  console.log('\n✨ Diagnostic complete! Check the logs above for any issues.');
  
})();
